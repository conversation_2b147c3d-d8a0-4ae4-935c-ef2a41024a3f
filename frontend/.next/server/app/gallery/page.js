(()=>{var e={};e.id=235,e.ids=[235],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},12412:e=>{"use strict";e.exports=require("assert")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},81630:e=>{"use strict";e.exports=require("http")},55591:e=>{"use strict";e.exports=require("https")},91645:e=>{"use strict";e.exports=require("net")},21820:e=>{"use strict";e.exports=require("os")},33873:e=>{"use strict";e.exports=require("path")},11723:e=>{"use strict";e.exports=require("querystring")},27910:e=>{"use strict";e.exports=require("stream")},34631:e=>{"use strict";e.exports=require("tls")},83997:e=>{"use strict";e.exports=require("tty")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},74288:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=r(70260),i=r(28203),a=r(25155),n=r.n(a),o=r(67292),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d=["",{children:["gallery",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1696)),"/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/src/app/gallery/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,71354)),"/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/src/app/gallery/page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/gallery/page",pathname:"/gallery",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},8493:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"7f1a725ced801efa5c09f337cdb0090ead786826aa":()=>s.L,"7fae63220dd22fc849b77502541c0047222ea55957":()=>s.Q,"7fcdd28dfe7818073dca49602bb865079a3abbba6d":()=>i.q});var s=r(97532),i=r(97932)},83976:(e,t,r)=>{Promise.resolve().then(r.bind(r,11993)),Promise.resolve().then(r.bind(r,21760)),Promise.resolve().then(r.bind(r,13575)),Promise.resolve().then(r.bind(r,90603)),Promise.resolve().then(r.t.bind(r,59607,23)),Promise.resolve().then(r.t.bind(r,71066,23))},47528:(e,t,r)=>{Promise.resolve().then(r.bind(r,74485)),Promise.resolve().then(r.bind(r,68544)),Promise.resolve().then(r.bind(r,51986)),Promise.resolve().then(r.bind(r,9283)),Promise.resolve().then(r.t.bind(r,28531,23)),Promise.resolve().then(r.t.bind(r,41902,23))},53211:(e,t,r)=>{"use strict";r.d(t,{AY:()=>l,MC:()=>a,Xd:()=>d,bK:()=>o,kR:()=>i,lt:()=>s,tT:()=>n,vW:()=>p,zg:()=>c});let s=`
  *[_type == "newsArticle"] | order(publishedAt desc)[0...3] {
    _id,
    title,
    slug,
    publishedAt,
    mainImage,
    summary
  }
`,i=`
  *[_type == "newsArticle"] | order(publishedAt desc) {
    _id,
    title,
    slug,
    publishedAt,
    mainImage,
    summary
  }
`,a=`
  *[_type == "event" && date > now()] | order(date asc)[0...5] {
    _id,
    title,
    slug,
    date,
    location,
    eventType,
    opponent,
    homeOrAway
  }
`,n=`
  *[_type == "event"] | order(date desc) {
    _id,
    title,
    slug,
    date,
    location,
    eventType,
    opponent,
    homeOrAway,
    result
  }
`,o=`
  *[_type == "event" && slug.current == $slug][0] {
    _id,
    title,
    slug,
    date,
    location,
    eventType,
    opponent,
    homeOrAway,
    result,
    description
  }
`,l=`
  *[_type == "sponsor"] | order(displayOrder asc) {
    _id,
    name,
    logo,
    websiteUrl,
    sponsorshipLevel
  }
`,d=`
  *[_type == "galleryImage"] | order(dateTaken desc) {
    _id,
    title,
    imageFile,
    dateTaken
  }
`,c=`
  *[_type == "player"] | order(jerseyNumber asc) {
    _id,
    name,
    position,
    jerseyNumber,
    image,
    stats,
    bio
  }
`,p=`
  *[_type == "staff"] | order(displayOrder asc) {
    _id,
    name,
    role,
    image,
    bio,
    contactInfo
  }
`},19405:(e,t,r)=>{"use strict";r.d(t,{S:()=>l,i:()=>c});var s=r(3709),i=r(25e3),a=r.n(i);let n="9at30otk",o="production",l=(0,s.UU)({projectId:n,dataset:o,apiVersion:"2024-05-23",useCdn:"undefined"!=typeof document}),d=a()({projectId:n,dataset:o});function c(e){return d.image(e)}},1696:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u,metadata:()=>d,revalidate:()=>c});var s=r(62740),i=r(11993),a=r(47735),n=r(35635),o=r(19405),l=r(53211);let d={title:"Gallery | Northern Nepalese United FC",description:"View photos from Northern Nepalese United FC's matches, training sessions, and community events."},c=60;async function p(){return await o.S.fetch(l.Xd)}async function u(){let e=await p();return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.default,{}),(0,s.jsxs)("main",{children:[(0,s.jsx)("section",{className:"bg-primary-800 text-white py-16",children:(0,s.jsxs)("div",{className:"container",children:[(0,s.jsx)("h1",{className:"text-4xl md:text-5xl font-bold mb-4",children:"Team Gallery"}),(0,s.jsx)("p",{className:"text-xl max-w-3xl",children:"Browse photos from our matches, training sessions, and community events."})]})}),(0,s.jsx)("section",{className:"py-16",children:(0,s.jsx)("div",{className:"container",children:e&&e.length>0?(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:e.map(e=>(0,s.jsxs)("div",{className:"relative aspect-square rounded-lg overflow-hidden shadow-md hover:shadow-lg transition",children:[(0,s.jsx)(n.default,{src:(0,o.i)(e.imageFile).url(),alt:e.title||"Gallery image",fill:!0,className:"object-cover hover:scale-105 transition-transform duration-300",sizes:"(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"}),(0,s.jsxs)("div",{className:"absolute inset-x-0 bottom-0 bg-gradient-to-t from-black/70 to-transparent p-4",children:[(0,s.jsx)("h3",{className:"text-white font-medium text-sm",children:e.title}),e.dateTaken&&(0,s.jsx)("p",{className:"text-white/80 text-xs",children:new Date(e.dateTaken).toLocaleDateString()})]})]},e._id))}):(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsxs)("div",{className:"bg-neutral-100 rounded-lg p-8 max-w-xl mx-auto",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-16 w-16 text-neutral-400 mx-auto mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})}),(0,s.jsx)("h2",{className:"text-2xl font-bold mb-2",children:"Gallery Coming Soon"}),(0,s.jsx)("p",{className:"text-neutral-600",children:"We're currently collecting photos from our recent events. Check back soon to see our gallery."})]})})})})]}),(0,s.jsx)(a.A,{})]})}},3342:(e,t,r)=>{"use strict";r.d(t,{Q:()=>i});var s=r(26248);let i=(0,s.createServerReference)("7fae63220dd22fc849b77502541c0047222ea55957",s.callServer,void 0,s.findSourceMapURL,"revalidateSyncTags")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,619,979,310,597,635,273],()=>r(74288));module.exports=s})();