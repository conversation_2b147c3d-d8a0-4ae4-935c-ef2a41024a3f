(()=>{var e={};e.id=706,e.ids=[706],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},12412:e=>{"use strict";e.exports=require("assert")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},81630:e=>{"use strict";e.exports=require("http")},55591:e=>{"use strict";e.exports=require("https")},91645:e=>{"use strict";e.exports=require("net")},21820:e=>{"use strict";e.exports=require("os")},33873:e=>{"use strict";e.exports=require("path")},11723:e=>{"use strict";e.exports=require("querystring")},27910:e=>{"use strict";e.exports=require("stream")},34631:e=>{"use strict";e.exports=require("tls")},83997:e=>{"use strict";e.exports=require("tty")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},85420:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d});var r=s(70260),n=s(28203),a=s(25155),i=s.n(a),l=s(67292),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d=["",{children:["events",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,6117)),"/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/src/app/events/[slug]/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,71354)),"/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/src/app/events/[slug]/page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/events/[slug]/page",pathname:"/events/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},8493:(e,t,s)=>{"use strict";s.r(t),s.d(t,{"7f1a725ced801efa5c09f337cdb0090ead786826aa":()=>r.L,"7fae63220dd22fc849b77502541c0047222ea55957":()=>r.Q,"7fcdd28dfe7818073dca49602bb865079a3abbba6d":()=>n.q});var r=s(97532),n=s(97932)},68878:(e,t,s)=>{Promise.resolve().then(s.bind(s,11993)),Promise.resolve().then(s.bind(s,21760)),Promise.resolve().then(s.bind(s,13575)),Promise.resolve().then(s.bind(s,90603)),Promise.resolve().then(s.t.bind(s,59607,23)),Promise.resolve().then(s.t.bind(s,71066,23))},31926:(e,t,s)=>{Promise.resolve().then(s.bind(s,74485)),Promise.resolve().then(s.bind(s,68544)),Promise.resolve().then(s.bind(s,51986)),Promise.resolve().then(s.bind(s,9283)),Promise.resolve().then(s.t.bind(s,28531,23)),Promise.resolve().then(s.t.bind(s,41902,23))},50458:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var r=s(62740),n=s(65228),a=s(35635),i=s(19405);let l={types:{image:({value:e})=>e?.asset?._ref?(0,r.jsx)("div",{className:"relative w-full h-96 my-6",children:(0,r.jsx)(a.default,{src:(0,i.i)(e).url(),alt:e.alt||"",fill:!0,className:"object-cover rounded-lg"})}):null},marks:{link:({children:e,value:t})=>{let s=t.href.startsWith("/")?void 0:"noreferrer noopener";return(0,r.jsx)("a",{href:t.href,rel:s,className:"text-blue-600 hover:underline",children:e})}}};function o({content:e}){return(0,r.jsx)(n.RK,{value:e,components:l})}},53211:(e,t,s)=>{"use strict";s.d(t,{AY:()=>o,MC:()=>a,Xd:()=>d,bK:()=>l,kR:()=>n,lt:()=>r,tT:()=>i,vW:()=>m,zg:()=>c});let r=`
  *[_type == "newsArticle"] | order(publishedAt desc)[0...3] {
    _id,
    title,
    slug,
    publishedAt,
    mainImage,
    summary
  }
`,n=`
  *[_type == "newsArticle"] | order(publishedAt desc) {
    _id,
    title,
    slug,
    publishedAt,
    mainImage,
    summary
  }
`,a=`
  *[_type == "event" && date > now()] | order(date asc)[0...5] {
    _id,
    title,
    slug,
    date,
    location,
    eventType,
    opponent,
    homeOrAway
  }
`,i=`
  *[_type == "event"] | order(date desc) {
    _id,
    title,
    slug,
    date,
    location,
    eventType,
    opponent,
    homeOrAway,
    result
  }
`,l=`
  *[_type == "event" && slug.current == $slug][0] {
    _id,
    title,
    slug,
    date,
    location,
    eventType,
    opponent,
    homeOrAway,
    result,
    description
  }
`,o=`
  *[_type == "sponsor"] | order(displayOrder asc) {
    _id,
    name,
    logo,
    websiteUrl,
    sponsorshipLevel
  }
`,d=`
  *[_type == "galleryImage"] | order(dateTaken desc) {
    _id,
    title,
    imageFile,
    dateTaken
  }
`,c=`
  *[_type == "player"] | order(jerseyNumber asc) {
    _id,
    name,
    position,
    jerseyNumber,
    image,
    stats,
    bio
  }
`,m=`
  *[_type == "staff"] | order(displayOrder asc) {
    _id,
    name,
    role,
    image,
    bio,
    contactInfo
  }
`},19405:(e,t,s)=>{"use strict";s.d(t,{S:()=>o,i:()=>c});var r=s(3709),n=s(25e3),a=s.n(n);let i="9at30otk",l="production",o=(0,r.UU)({projectId:i,dataset:l,apiVersion:"2024-05-23",useCdn:"undefined"!=typeof document}),d=a()({projectId:i,dataset:l});function c(e){return d.image(e)}},6117:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>v,generateMetadata:()=>h,generateStaticParams:()=>u,revalidate:()=>m});var r=s(62740),n=s(11993),a=s(47735),i=s(59607),l=s.n(i),o=s(19405),d=s(50458),c=s(53211);let m=60;async function p(e){return await o.S.fetch(c.bK,{slug:e})}async function x(){return await o.S.fetch(`
    *[_type == "event"] {
      "slug": slug.current
    }
  `)}async function u(){return(await x()).map(e=>({slug:e.slug}))}async function h({params:e}){let t=await p(e.slug);return t?{title:`${t.title} | Northern Nepalese United FC`,description:`Details about ${t.title} at Northern Nepalese United FC`}:{title:"Event Not Found | Northern Nepalese United FC"}}async function v({params:e}){let t=await p(e.slug);if(!t)return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(n.default,{}),(0,r.jsx)("main",{className:"py-16",children:(0,r.jsx)("div",{className:"container",children:(0,r.jsx)("div",{className:"text-center py-12",children:(0,r.jsxs)("div",{className:"bg-neutral-100 rounded-lg p-8 max-w-2xl mx-auto",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-16 w-16 text-neutral-400 mx-auto mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,r.jsx)("h1",{className:"text-2xl font-bold mb-2",children:"Event Not Found"}),(0,r.jsx)("p",{className:"text-neutral-600 mb-6",children:"The event you're looking for doesn't exist or has been removed."}),(0,r.jsx)(l(),{href:"/events",className:"btn-primary",children:"Back to Events"})]})})})}),(0,r.jsx)(a.A,{})]});let s=new Date(t.date),i=s.toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"}),o=s.toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit"});return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(n.default,{}),(0,r.jsxs)("main",{children:[(0,r.jsx)("section",{className:`text-white py-16 ${"match"===t.eventType?"bg-primary-800":"training"===t.eventType?"bg-secondary-800":"bg-neutral-800"}`,children:(0,r.jsx)("div",{className:"container",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsx)("div",{className:"mb-4",children:(0,r.jsxs)(l(),{href:"/events",className:"inline-flex items-center text-white/80 hover:text-white",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z",clipRule:"evenodd"})}),"Back to Events"]})}),(0,r.jsx)("h1",{className:"text-4xl md:text-5xl font-bold mb-4",children:t.title}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-4 items-center",children:[(0,r.jsxs)("p",{className:"text-lg opacity-90",children:[i," at ",o]}),t.eventType&&(0,r.jsx)("span",{className:`text-xs px-3 py-1 rounded-full uppercase font-semibold ${"match"===t.eventType?"bg-primary-100 text-primary-800":"training"===t.eventType?"bg-secondary-100 text-secondary-800":"bg-neutral-100 text-neutral-800"}`,children:t.eventType})]})]})})}),(0,r.jsx)("section",{className:"py-16",children:(0,r.jsx)("div",{className:"container",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-md p-8 mb-8",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"Event Details"}),(0,r.jsxs)("ul",{className:"space-y-4",children:[(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 mr-3 text-primary-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-semibold text-neutral-700",children:"Date & Time"}),(0,r.jsxs)("p",{children:[i," at ",o]})]})]}),t.location&&(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 mr-3 text-primary-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-semibold text-neutral-700",children:"Location"}),(0,r.jsx)("p",{children:t.location})]})]}),"match"===t.eventType&&t.opponent&&(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 mr-3 text-primary-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-semibold text-neutral-700",children:"Opponent"}),(0,r.jsx)("p",{children:t.opponent})]})]}),"match"===t.eventType&&t.homeOrAway&&(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 mr-3 text-primary-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-semibold text-neutral-700",children:"Venue"}),(0,r.jsx)("p",{children:"Home"===t.homeOrAway?"Home Game":"Away Game"})]})]}),"match"===t.eventType&&t.result&&(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 mr-3 text-primary-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-semibold text-neutral-700",children:"Result"}),(0,r.jsx)("p",{children:t.result})]})]})]})]}),(0,r.jsxs)("div",{className:"bg-neutral-50 p-6 rounded-lg",children:[(0,r.jsx)("h3",{className:"text-xl font-bold mb-3",children:"Need to Know"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2 text-primary-600",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),(0,r.jsx)("span",{children:"Please arrive 30 minutes before the scheduled time"})]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2 text-primary-600",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),(0,r.jsx)("span",{children:"Bring appropriate footwear and water bottle"})]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2 text-primary-600",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),(0,r.jsx)("span",{children:"Parking is available at the venue"})]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2 text-primary-600",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),(0,r.jsx)("span",{children:"Contact team manager for any questions"})]})]})]})]})}),t.description&&(0,r.jsxs)("div",{className:"prose prose-lg max-w-none",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"Event Description"}),(0,r.jsx)(d.A,{content:t.description})]}),(0,r.jsx)("div",{className:"mt-12 pt-8 border-t border-neutral-200",children:(0,r.jsx)(l(),{href:"/events",className:"btn-primary",children:"Back to Events"})})]})})})]}),(0,r.jsx)(a.A,{})]})}},3342:(e,t,s)=>{"use strict";s.d(t,{Q:()=>n});var r=s(26248);let n=(0,r.createServerReference)("7fae63220dd22fc849b77502541c0047222ea55957",r.callServer,void 0,r.findSourceMapURL,"revalidateSyncTags")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,619,979,310,597,635,228,273],()=>s(85420));module.exports=r})();